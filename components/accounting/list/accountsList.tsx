import Link from 'next/link';
import { FC } from 'react';

import Table from '@/components/global/table/table';
import { Account } from 'models';
import moment from 'moment';

interface Props {
  accountsList: Account[];
}

const AccountsList: FC<Props> = ({ accountsList }) => {
  const onClickForSort = (name: string) => {
    // console.log(name);
  };

  const formatAmount = (amount: number) => {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      currencyDisplay: 'code',
    }).format(amount);
    return formatted.replace('BDT', 'TK');
  };

  const getTransactionTypeColor = (type: string) => {
    return type === 'credit' ? 'text-success' : 'text-danger';
  };

  const columns = [
    {
      label: 'Transaction ID',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/accounting/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <a className="text-decoration-none">
              {data?.[key]?.substring(0, 8) || ''}...
            </a>
          </Link>
        </td>
      ),
    },
    {
      label: 'Transaction Type',
      path: 'transactionType',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <span className={`badge ${data?.[key] === 'credit' ? 'bg-success' : 'bg-danger'}`}>
            {data?.[key]?.toUpperCase() || ''}
          </span>
        </td>
      ),
    },
    {
      label: 'Reference',
      path: 'reference',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <span className="badge bg-secondary">
            {data?.[key]?.toUpperCase() || ''}
          </span>
        </td>
      ),
    },
    {
      label: 'Reference Type',
      path: 'referenceType',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          {data?.[key]?.toUpperCase() || ''}
        </td>
      ),
    },
    {
      label: 'Amount',
      path: 'amount',
      content: (data: any, key: any, index: any) => (
        <td className={`text-center align-middle fw-bold ${getTransactionTypeColor(data?.transactionType)}`}>
          {formatAmount(data?.[key] || 0)}
        </td>
      ),
    },
    {
      label: 'View Details',
      path: 'id',
      content: (data: any, key: any, index: any) => (
        <td className="text-center align-middle">
          <Link
            href={{
              pathname: `/accounting/view/[id]`,
              query: { id: data?.[key] },
            }}
            passHref
            legacyBehavior
          >
            <button className="btn btn-default btn-outline-primary btn-sm">
              <span>
                <i className="bi bi-eye me-2 align-middle"></i>
              </span>
              View
            </button>
          </Link>
        </td>
      ),
    },
  ];

  return (
    <>
      <div className="card border-1 mt-3 rounded px-2">
        <div className="card-body">
          <Table
            items={accountsList}
            columns={columns}
            onClickForSort={onClickForSort}
          />
        </div>
      </div>
    </>
  );
};

export default AccountsList;
