const SubMenuIcon = <i className="bi bi-record-circle" />;
const DashboardIcon = <i className="bi bi-tv" />;
const CatalogIcon = <i className="bi bi-card-list" />;
const PromotionIcon = <i className="bi bi-tags" />;
const UsersIcon = <i className="bi bi-people-fill" />;
const SettingsIcon = <i className="bi bi-box" />;
const BlogIcon = <i className="bi bi-blockquote-right"></i>;
const ReportsIcon = <i className="bi bi-graph-up-arrow" />;
const SalesIcon = <i className="bi bi-cash" />;
const ClubIcon = <i className="bi bi-people"></i>;
const TrainingIcon = <i className="bi bi-heart-pulse"></i>;
const challengeIcon = <i className="bi bi-c-square"></i>;
const ExerciseIcon = <i className="bi bi-speedometer"></i>;
const QuestionIcon = <i className="bi bi-question-octagon"></i>;
const SubscriptionIcon = <i className="bi bi-stack"></i>;
const WarehouseIcon = <i className="bi bi-box-seam"></i>;
const PollsIcon = <i className="bi bi-bar-chart-fill" />;
const EventIcon = <i className="bi bi-calendar-event" />;
const CoachIcon = <i className="bi bi-person-workspace" />;
const AccountingIcon = <i className="bi bi-calculator" />;

export interface ISidebarSubmenu {
  name: string;
  to: string;
  icon: JSX.Element;
}

export interface ISidebarData {
  id: number;
  name: string;
  to: string;
  icon: JSX.Element;
  subMenus: ISidebarSubmenu[];
}

export const SidebarData: ISidebarData[] = [
  {
    id: 0,
    name: 'Dashboard',
    to: '/',
    icon: DashboardIcon,
    subMenus: [],
  },
  {
    id: 1,
    name: 'Fit Market',
    to: '',
    icon: CatalogIcon,
    subMenus: [
      {
        name: 'Products',
        to: '/Product',
        icon: SubMenuIcon,
      },
      {
        name: 'Categories',
        to: '/Category',
        icon: SubMenuIcon,
      },
      {
        name: 'Manufacturers',
        to: '/Manufacturer',
        icon: SubMenuIcon,
      },
      {
        name: 'Brands',
        to: '/Brands',
        icon: SubMenuIcon,
      },
      {
        name: 'Product Reviews',
        to: '/ProductReview',
        icon: SubMenuIcon,
      },
      {
        name: 'Product Tags',
        to: '/ProductTags',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 2,
    name: 'Club Management',
    to: '',
    icon: ClubIcon,
    subMenus: [
      {
        name: 'Clubs',
        to: '/clubManagement/clubs',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 3,
    name: 'Exercise Management',
    to: '',
    icon: ExerciseIcon,
    subMenus: [
      {
        name: 'Exercises',
        to: '/exercise-module/exercises',
        icon: SubMenuIcon,
      },
      {
        name: 'Exercise Category',
        to: '/exercise-module/category',
        icon: SubMenuIcon,
      },
      {
        name: 'Muscle Group',
        to: '/exercise-module/muscle-group',
        icon: SubMenuIcon,
      },
      {
        name: 'Body Building Program',
        to: '/exercise-module/body-building-program',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 4,
    name: 'Trainings',
    to: '/training',
    icon: TrainingIcon,
    subMenus: [
      {
        name: 'Beginner',
        to: '/training/beginner',
        icon: SubMenuIcon,
      },
      {
        name: 'Intermediate',
        to: '/training/intermediate',
        icon: SubMenuIcon,
      },
      {
        name: 'Advance',
        to: '/training/advance',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 5,
    name: 'Challenges',
    to: '/challenges',
    icon: challengeIcon,
    subMenus: [
      {
        name: 'Accepted List',
        to: '/challenges/accepted-list',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 6,
    name: 'Polls',
    to: '/poll',
    icon: PollsIcon,
    subMenus: [
      {
        name: 'Create Poll',
        to: '/poll/create',
        icon: SubMenuIcon,
      },
      {
        name: 'Manage Polls',
        to: '/poll',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 7,
    name: 'Events',
    to: '/event',
    icon: EventIcon,
    subMenus: [
      {
        name: 'Create Event',
        to: '/event/create',
        icon: SubMenuIcon,
      },
      {
        name: 'Manage Events',
        to: '/event',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 8,
    name: 'Coach',
    to: '/coach',
    icon: CoachIcon,
    subMenus: [
      {
        name: 'Coach Profile',
        to: '/coach',
        icon: SubMenuIcon,
      },
      {
        name: 'Coach Category',
        to: '/coach/category',
        icon: SubMenuIcon,
      },
      {
        name: 'Coach Sub Category',
        to: '/coach/subCategory',
        icon: SubMenuIcon,
      },
      {
        name: 'Withdraw',
        to: '/coach/withdraw',
        icon: SubMenuIcon,
      },
      {
        name: 'Refund',
        to: '/coach/refund',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 9,
    name: 'Settings',
    to: '',
    icon: SettingsIcon,
    subMenus: [
      {
        name: 'Point Settings',
        to: '/settings/point',
        icon: SubMenuIcon,
      },
      {
        name: 'Payment Methods',
        to: '/settings/payment-methods',
        icon: SubMenuIcon,
      },
      {
        name: 'For Free Shipment',
        to: '/settings/free-shipment',
        icon: SubMenuIcon,
      },
      {
        name: 'Subscription Packages',
        to: '/subscriptions',
        icon: SubMenuIcon,
      },
    ],
  },
  {
    id: 10,
    name: 'Blog',
    to: '/blog',
    icon: BlogIcon,
    subMenus: [],
  },
  {
    id: 11,
    name: 'Subscriber List',
    to: '/subscriber-list',
    icon: SubscriptionIcon,
    subMenus: [],
  },
  {
    id: 12,
    name: 'Warehouses',
    to: '/warehouses',
    icon: WarehouseIcon,
    subMenus: [],
  },
  {
    id: 13,
    name: 'Accounting',
    to: '/accounting',
    icon: AccountingIcon,
    subMenus: [],
  },
];
