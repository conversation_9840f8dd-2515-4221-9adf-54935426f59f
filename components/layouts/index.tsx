import Axios from 'axios';
import { Admin } from 'models';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';

import 'react-toastify/dist/ReactToastify.css';

import { userAPI } from '../../APIs';
import { useAppSelector } from '../../redux-hooks';

import Headerbar from './components/headerbar';
import Sidebar from './components/sidebar';
import ConfirmationModal from '../common/modal';

interface Props {
  children: React.ReactNode;
}

const Layout: React.FC<Props> = ({ children }) => {
  const [userData, setUserData] = useState<Admin>();
  const [showSidebar, setShowSidebar] = useState(true);

  const { pathname } = useRouter();
  const router = useRouter();
  const token = useAppSelector((state) => state.persistedReducer.auth.token);
  const show = useAppSelector((state) => state.persistedReducer.modal.show);

  if (!token && !pathname.includes('/account')) {
    router.push('/account/login');
  } else {
    Axios.defaults.headers.common = {
      Authorization: `Bearer ${token}`,
    };
  }

  useEffect(() => {
    typeof document !== undefined
      ? require('bootstrap/dist/js/bootstrap')
      : null;
  }, []);

  const toggleSidebar = (sideBarStatus: boolean) => {
    setShowSidebar(sideBarStatus);
  };

  if (pathname.includes('/account')) {
    return (
      <>
        <ToastContainer />
        {children}
      </>
    );
  }

  return (
    <>
      {show === true && <ConfirmationModal show={show} />}
      <ToastContainer />
      <div className="d-flex">
        <Sidebar toggleSidebar={toggleSidebar} showSidebar={showSidebar} />
        <div
          className="d-flex flex-column"
          style={{
            width: '100%',
            marginLeft: showSidebar ? '250px' : '60px',
            transition: 'all 0.3s linear',
          }}
        >
          <div style={{ height: '56px', width: '100%' }}>
            <Headerbar
              toggleSidebar={toggleSidebar}
              showSidebar={showSidebar}
              displayName={userData?.displayName!}
            />
          </div>
          <div>{children}</div>
        </div>
      </div>
    </>
  );
};

export default Layout;
