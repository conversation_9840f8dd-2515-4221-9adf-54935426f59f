import type { NextPage } from 'next';
import { useEffect, useState } from 'react';

import { userAPI } from '@/APIs';
import PrevNextPagination from '@/components/common/newPagination';
import AccountsList from '@/components/accounting/list/accountsList';
import { Account } from 'models';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { setAccounts, setLoading, setError } from 'toolkit/accountingSlice';

const Accounting: NextPage = () => {
  const dispatch = useAppDispatch();
  const { accounts, loading, error } = useAppSelector(
    (state) => state.persistedReducer.accounting
  );

  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(10);
  const [disableNext, setDisableNext] = useState(false);

  useEffect(() => {
    const getAccountsList = async () => {
      try {
        dispatch(setLoading(true));
        const res = await userAPI.getAccounts();
        
        if (res.data) {
          dispatch(setAccounts(res.data));
          if (res.data.length === 0) {
            setDisableNext(true);
          } else {
            setDisableNext(false);
          }
        } else {
          dispatch(setError(res.error?.message || 'Failed to fetch accounts'));
          toast.error(res.error?.message || 'Failed to fetch accounts');
        }
      } catch (error: any) {
        dispatch(setError(error.message || 'An error occurred'));
        toast.error(error.message || 'An error occurred');
      }
    };

    getAccountsList();
  }, [dispatch, skip, limit]);

  if (loading) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="px-5">
        <div className="alert alert-danger mt-3" role="alert">
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </div>
      </main>
    );
  }

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">
            <i className="bi bi-calculator me-2"></i>
            Accounting Transactions
          </div>
          <div className="text-muted">
            Total: {accounts?.length || 0} transactions
          </div>
        </div>
        <div>
          {accounts?.length! > 0 ? (
            <>
              <AccountsList accountsList={accounts!} />
              <PrevNextPagination
                skip={skip}
                setSkip={setSkip}
                limit={limit}
                disableNext={disableNext}
              />
            </>
          ) : (
            <div className="card border-1 mt-3 rounded">
              <div className="card-body text-center py-5">
                <i className="bi bi-inbox display-1 text-muted"></i>
                <h4 className="mt-3 text-muted">No transactions found</h4>
                <p className="text-muted">There are no accounting transactions to display.</p>
              </div>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default Accounting;
