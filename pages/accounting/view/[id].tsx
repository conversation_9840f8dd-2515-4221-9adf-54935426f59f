import type { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';

import { userAPI } from '@/APIs';
import AccountDetails from '@/components/accounting/accountDetails';
import { Account } from 'models';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from '@/redux-hooks';
import { setSelectedAccount, setLoading, setError } from 'toolkit/accountingSlice';

const AccountView: NextPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  
  const { selectedAccount, loading, error } = useAppSelector(
    (state) => state.persistedReducer.accounting
  );

  useEffect(() => {
    if (!id) return;

    const getAccount = async () => {
      try {
        dispatch(setLoading(true));
        const res = await userAPI.getAccount(id as string);
        
        if (res.data) {
          dispatch(setSelectedAccount(res.data));
        } else {
          dispatch(setError(res.error?.message || 'Failed to fetch account details'));
          toast.error(res.error?.message || 'Failed to fetch account details');
        }
      } catch (error: any) {
        dispatch(setError(error.message || 'An error occurred'));
        toast.error(error.message || 'An error occurred');
      }
    };

    getAccount();
  }, [id, dispatch]);

  if (loading) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Account Details</div>
          <Link href="/accounting" className="btn btn-secondary">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Accounting
          </Link>
        </div>
        <div className="alert alert-danger mt-3" role="alert">
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </div>
      </main>
    );
  }

  if (!selectedAccount) {
    return (
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">Account Details</div>
          <Link href="/accounting" className="btn btn-secondary">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Accounting
          </Link>
        </div>
        <div className="alert alert-warning mt-3" role="alert">
          <i className="bi bi-info-circle me-2"></i>
          Account not found.
        </div>
      </main>
    );
  }

  return (
    <>
      <main className="px-5">
        <div className="d-flex justify-content-between align-items-center mt-3">
          <div className="fs-2">
            <i className="bi bi-receipt me-2"></i>
            Account Details
          </div>
          <Link href="/accounting" className="btn btn-secondary">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Accounting
          </Link>
        </div>
        
        <AccountDetails account={selectedAccount} />
      </main>
    </>
  );
};

export default AccountView;
