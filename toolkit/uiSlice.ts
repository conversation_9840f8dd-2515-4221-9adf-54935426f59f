import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface uiState {
  showSidebar: boolean;
}

const initialState: uiState = {
  showSidebar: true,
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state, action: PayloadAction<boolean>) => {
      state.showSidebar = action.payload;
    },
    setSidebarVisible: (state) => {
      state.showSidebar = true;
    },
    setSidebarHidden: (state) => {
      state.showSidebar = false;
    },
  },
});

export const { toggleSidebar, setSidebarVisible, setSidebarHidden } = uiSlice.actions;

export default uiSlice.reducer;
